import React from 'react'
import { Menu } from 'antd'
import { Link, useLocation } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import {
  HomeOutlined,
  RobotOutlined,
  ThunderboltOutlined,
  PartitionOutlined,
  MessageOutlined,
  ToolOutlined,
  TrophyOutlined,
  GiftOutlined
} from '@ant-design/icons'

export const MainNavigation: React.FC = () => {
  const location = useLocation()
  const { t } = useTranslation()

  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: <Link to="/">{t('nav.home')}</Link>
    },
    {
      key: '/models',
      icon: <RobotOutlined />,
      label: <Link to="/resources?type=fine_tuned_model">{t('resourceTypes.fine_tuned_model')}</Link>
    },
    {
      key: '/lora',
      icon: <ThunderboltOutlined />,
      label: <Link to="/resources?type=lora">{t('resourceTypes.lora')}</Link>
    },
    {
      key: '/workflows',
      icon: <PartitionOutlined />,
      label: <Link to="/resources?type=workflow">{t('resourceTypes.workflow')}</Link>
    },
    {
      key: '/prompts',
      icon: <MessageOutlined />,
      label: <Link to="/resources?type=prompt">{t('resourceTypes.prompt')}</Link>
    },
    {
      key: '/tools',
      icon: <ToolOutlined />,
      label: <Link to="/resources?type=tool">{t('resourceTypes.tool')}</Link>
    },
    {
      key: '/challenges',
      icon: <TrophyOutlined />,
      label: <Link to="/challenges">{t('nav.challenges', { defaultValue: '挑战' })}</Link>
    },
    {
      key: '/bounties',
      icon: <GiftOutlined />,
      label: <Link to="/bounties">{t('nav.bounties', { defaultValue: '悬赏' })}</Link>
    }
  ]

  return (
    <div style={{
      height: '50px',
      backgroundColor: '#1f1f1f',
      borderBottom: '1px solid #2a2a2a',
      display: 'flex',
      alignItems: 'center',
      padding: '0 24px',
      boxShadow: '0 1px 4px rgba(0,0,0,0.1)'
    }}>
      <Menu
        mode="horizontal"
        selectedKeys={[location.pathname]}
        items={menuItems}
        theme="dark"
        style={{
          border: 'none',
          backgroundColor: 'transparent',
          flex: 1,
          justifyContent: 'flex-start',
          fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
          fontSize: '14px',
          fontWeight: '300',
          color: '#fff'
        }}
        className="main-navigation-menu"
      />
    </div>
  )
}
