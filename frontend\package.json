{"name": "aigc-service-hub-frontend", "version": "1.0.0", "description": "Frontend application for AIGC Service Hub - AI Resource Trading Platform", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}", "type-check": "tsc --noEmit"}, "keywords": ["ai", "aigc", "trading", "platform", "react", "typescript", "vite"], "author": "AIGC-OFFICE", "license": "MIT", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.1", "react-query": "^3.39.3", "axios": "^1.4.0", "zustand": "^4.3.9", "react-hook-form": "^7.45.1", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.4.1", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "@ant-design/pro-components": "^2.6.48", "dayjs": "^1.11.10", "clsx": "^1.2.1", "tailwind-merge": "^1.13.2", "framer-motion": "^10.12.18", "react-intersection-observer": "^9.5.2", "date-fns": "^2.30.0", "react-paypal-js": "^8.1.3"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.4.1", "typescript": "^5.0.2", "vite": "^4.4.5", "vitest": "^0.33.0", "@vitest/ui": "^0.33.0", "c8": "^8.0.0", "tailwindcss": "^3.3.3", "autoprefixer": "^10.4.14", "postcss": "^8.4.27", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/user-event": "^14.4.3", "jsdom": "^22.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}