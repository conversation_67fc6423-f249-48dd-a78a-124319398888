import React, { useState } from 'react'
import { Layout, Row, Col, Input, Button, Space, Typography, Dropdown } from 'antd'
import {
  SearchOutlined,
  PlusOutlined,
  ShareAltOutlined,
  LoginOutlined,
  UserAddOutlined,
  DownOutlined,
  RobotOutlined,
  ToolOutlined,
  FileTextOutlined,
  BulbOutlined,
  UserOutlined
} from '@ant-design/icons'
import { Link, useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { useAuth } from '@/hooks/useAuth'
import LanguageSwitcher from '../LanguageSwitcher'

const { Header } = Layout
const { Search } = Input
const { Text } = Typography

export const TopHeader: React.FC = () => {
  const { isAuthenticated } = useAuth()
  const navigate = useNavigate()
  const { t } = useTranslation()



  return (
    <Header
      style={{
        height: '100px',
        backgroundColor: '#1a1a1a',
        borderBottom: '1px solid #2a2a2a',
        padding: '0 24px',
        display: 'flex',
        alignItems: 'center',
        boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
      }}
    >
      <Row justify="space-between" align="middle" style={{ width: '100%' }}>
        {/* Logo */}
        <Col style={{
          display: 'flex',
          alignItems: 'center',
          height: '100%'
        }}>
          <img
            src="/aigc-logo-optimized.svg"
            alt="AIGC Service Hub"
            style={{
              height: '80px',
              width: 'auto',
              maxWidth: '280px',
              objectFit: 'contain',
              display: 'block',
              cursor: 'pointer',
              transform: 'translateY(12px)',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(12px) scale(1.05)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(12px) scale(1)'
            }}
            onMouseDown={(e) => {
              e.currentTarget.style.transform = 'translateY(12px) scale(0.95)'
            }}
            onMouseUp={(e) => {
              e.currentTarget.style.transform = 'translateY(12px) scale(1.05)'
            }}
            onClick={() => navigate('/')}
            onError={(e) => {
              console.error('LOGO加载失败，使用备用LOGO')
              e.currentTarget.src = '/aigc-logo.svg'
            }}
          />
        </Col>

        {/* Spacer */}
        <Col flex="auto"></Col>

        {/* Search */}
        <Col
          className="search-container"
          style={{
            marginRight: '24px',
            display: 'flex',
            alignItems: 'center',
            height: '100px',
            flex: 1,
            justifyContent: 'center'
          }}
        >
          <Search
            placeholder={t('resources.searchPlaceholder')}
            allowClear
            enterButton={<SearchOutlined />}
            size="large"
            className="responsive-search"
            style={{
              fontSize: '14px',
              '& .ant-input': {
                backgroundColor: 'transparent',
                borderColor: '#3a3a3a',
                color: '#fff',
                fontSize: '14px'
              },
              '& .ant-input::placeholder': {
                color: '#888',
                fontSize: '14px'
              },
              '& .ant-btn-primary': {
                background: 'linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%)',
                borderColor: '#8B5CF6',
                boxShadow: '0 2px 8px rgba(139, 92, 246, 0.3)'
              }
            }}
            onSearch={(value) => {
              if (value.trim()) {
                navigate(`/resources?search=${encodeURIComponent(value)}`)
              }
            }}
          />
        </Col>

        {/* Actions - 新顺序：发布、登录（注册）、分享、语言 */}
        <Col>
          <Space size="middle">
            {/* Publish Dropdown */}
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'lora',
                    icon: <RobotOutlined />,
                    label: 'LoRA模型',
                    onClick: () => navigate('/upload/lora')
                  },
                  {
                    key: 'workflow',
                    icon: <ToolOutlined />,
                    label: '工作流',
                    onClick: () => navigate('/upload/workflow')
                  },
                  {
                    key: 'tool',
                    icon: <ToolOutlined />,
                    label: '工具',
                    onClick: () => navigate('/upload/tool')
                  },
                  {
                    key: 'prompt',
                    icon: <FileTextOutlined />,
                    label: '提示词',
                    onClick: () => navigate('/upload/prompt')
                  },
                  {
                    key: 'model',
                    icon: <BulbOutlined />,
                    label: '微调模型',
                    onClick: () => navigate('/upload/model')
                  }
                ]
              }}
              placement="bottomRight"
            >
              <Button
                type="primary"
                icon={<PlusOutlined style={{ fontSize: '14px' }} />}
                size="large"
                style={{
                  background: 'linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%)',
                  borderColor: '#8B5CF6',
                  boxShadow: '0 4px 12px rgba(139, 92, 246, 0.3)',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
                  fontWeight: 'normal'
                }}
              >
                <Space>
                  {t('common.upload')}
                  <DownOutlined style={{ fontSize: '14px' }} />
                </Space>
              </Button>
            </Dropdown>

            {/* Auth Buttons */}
            {!isAuthenticated && (
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'login',
                      icon: <LoginOutlined />,
                      label: t('nav.login'),
                      onClick: () => navigate('/login')
                    },
                    {
                      key: 'register',
                      icon: <UserAddOutlined />,
                      label: t('nav.register'),
                      onClick: () => navigate('/register')
                    }
                  ]
                }}
                placement="bottomRight"
              >
                <Button
                  icon={<LoginOutlined style={{ fontSize: '14px' }} />}
                  size="large"
                  style={{
                    backgroundColor: '#2a2a2a',
                    borderColor: '#3a3a3a',
                    color: '#fff',
                    borderRadius: '8px',
                    fontSize: '14px',
                    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
                    fontWeight: 'normal'
                  }}
                >
                  <Space>
                    {t('nav.login')}
                    <DownOutlined style={{ fontSize: '14px' }} />
                  </Space>
                </Button>
              </Dropdown>
            )}

            {/* Share Dropdown */}
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'behance',
                    icon: <span style={{ fontSize: '16px' }}>🎨</span>,
                    label: 'Behance',
                    onClick: () => {
                      const url = encodeURIComponent(window.location.href)
                      const title = encodeURIComponent('AIGC Service Hub - AI创作资源平台')
                      window.open(`https://www.behance.net/`, '_blank')
                    }
                  },
                  {
                    key: 'dribbble',
                    icon: <span style={{ fontSize: '16px' }}>🏀</span>,
                    label: 'Dribbble',
                    onClick: () => {
                      const url = encodeURIComponent(window.location.href)
                      window.open(`https://dribbble.com/`, '_blank')
                    }
                  },
                  {
                    key: 'pinterest',
                    icon: <span style={{ fontSize: '16px' }}>📌</span>,
                    label: 'Pinterest',
                    onClick: () => {
                      const url = encodeURIComponent(window.location.href)
                      const description = encodeURIComponent('AIGC Service Hub - AI创作资源平台')
                      window.open(`https://pinterest.com/pin/create/button/?url=${url}&description=${description}`, '_blank')
                    }
                  },
                  {
                    key: 'instagram',
                    icon: <span style={{ fontSize: '16px' }}>📷</span>,
                    label: 'Instagram',
                    onClick: () => {
                      window.open('https://www.instagram.com/', '_blank')
                    }
                  },
                  {
                    type: 'divider'
                  },
                  {
                    key: 'copy',
                    icon: <ShareAltOutlined />,
                    label: '复制链接',
                    onClick: () => {
                      navigator.clipboard.writeText(window.location.href)
                      // message.success('链接已复制到剪贴板')
                    }
                  }
                ]
              }}
              placement="bottomRight"
            >
              <Button
                icon={<ShareAltOutlined style={{ fontSize: '14px' }} />}
                size="large"
                style={{
                  backgroundColor: '#2a2a2a',
                  borderColor: '#3a3a3a',
                  color: '#fff',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
                  fontWeight: 'normal'
                }}
              >
                <Space>
                  {t('common.share', { defaultValue: '分享' })}
                  <DownOutlined style={{ fontSize: '14px' }} />
                </Space>
              </Button>
            </Dropdown>

            {/* Language Switcher */}
            <LanguageSwitcher />
          </Space>
        </Col>
      </Row>
    </Header>
  )
}
